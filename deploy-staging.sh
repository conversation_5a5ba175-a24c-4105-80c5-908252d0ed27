#!/bin/bash
set -euo pipefail

echo "🚀 JobbLogg Staging Deployment Script"
echo "======================================"

# Verify we're in the right directory
if [ ! -f "docker-compose.staging.yml" ]; then
    echo "❌ docker-compose.staging.yml not found. Run from project root."
    exit 1
fi

if [ ! -f ".env.staging" ]; then
    echo "❌ .env.staging not found. Run from project root."
    exit 1
fi

echo "📋 Verifying staging configuration..."

# Check that build args are correctly resolved
echo "🔍 Checking resolved docker-compose configuration..."
docker compose -f docker-compose.staging.yml --env-file .env.staging config | sed -n '/build:/,/image:/p'

# Verify BUILD_ENV is set to staging
BUILD_ENV_CHECK=$(docker compose -f docker-compose.staging.yml --env-file .env.staging config | grep -A 10 "build:" | grep "BUILD_ENV:" | head -1 || echo "")
if [[ "$BUILD_ENV_CHECK" == *"staging"* ]]; then
    echo "✅ BUILD_ENV correctly set to staging"
else
    echo "❌ BUILD_ENV not set to staging in resolved config:"
    echo "$BUILD_ENV_CHECK"
    exit 1
fi

# Verify VITE_CONVEX_URL is set to staging
CONVEX_URL_CHECK=$(docker compose -f docker-compose.staging.yml --env-file .env.staging config | grep -A 10 "build:" | grep "VITE_CONVEX_URL:" | head -1 || echo "")
if [[ "$CONVEX_URL_CHECK" == *"enchanted-quail-174"* ]]; then
    echo "✅ VITE_CONVEX_URL correctly set to staging"
else
    echo "❌ VITE_CONVEX_URL not set to staging URL in resolved config:"
    echo "$CONVEX_URL_CHECK"
    exit 1
fi

echo "🏗️ Building staging frontend (no cache)..."
docker compose -f docker-compose.staging.yml --env-file .env.staging build --no-cache frontend

echo "🔍 Post-build verification..."
# Get the built image name
IMAGE_NAME=$(docker compose -f docker-compose.staging.yml --env-file .env.staging config | grep "image:" | head -1 | awk '{print $2}' || echo "")
if [ -z "$IMAGE_NAME" ]; then
    # If no explicit image name, use the default compose naming
    PROJECT_NAME=$(basename "$(pwd)" | tr '[:upper:]' '[:lower:]')
    IMAGE_NAME="${PROJECT_NAME}_frontend"
fi

echo "Verifying image: $IMAGE_NAME"

# Create container without starting it
CID="$(docker create "$IMAGE_NAME")"
trap 'docker rm -f "$CID" >/dev/null 2>&1 || true' EXIT

# Copy assets out for verification
mkdir -p staging-verify
copied=0
for P in /app/dist /usr/share/nginx/html; do
    if docker cp "$CID:$P" "staging-verify/$(echo "$P" | sed 's|/|_|g')" 2>/dev/null; then
        echo "Copied assets from: $P"
        copied=1
        break
    fi
done

if [ "$copied" -eq 0 ]; then
    echo "❌ Could not copy assets from container"
    exit 1
fi

# Verify staging URL exists and prod URL doesn't
echo "🔍 Verifying staging URL is baked into bundle..."
if grep -R "https://enchanted-quail-174\.convex\.cloud" staging-verify >/dev/null; then
    echo "✅ Staging URL found in bundle"
else
    echo "❌ Staging URL not found in bundle"
    exit 1
fi

echo "🔍 Verifying prod URL is NOT in staging bundle..."
if ! grep -R "https://standing-aardvark-575\.convex\.cloud" staging-verify >/dev/null; then
    echo "✅ Prod URL not found in staging bundle"
else
    echo "❌ Prod URL leaked into staging bundle"
    exit 1
fi

# Cleanup verification files
rm -rf staging-verify

echo "🚀 Starting staging services..."
docker compose -f docker-compose.staging.yml --env-file .env.staging up -d frontend

echo "✅ Staging deployment completed successfully!"
echo "🌐 Staging should be available at: http://localhost:5175"
