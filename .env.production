# JobbLogg Production Environment Variables
# This file contains production-specific configuration
# Secrets are provided via GitHub Actions environment variables

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
NODE_ENV=production
BUILD_ENV=production
BUILD_TARGET=production
COMPOSE_ENV=prod

# =============================================================================
# PORT CONFIGURATION
# =============================================================================
FRONTEND_PORT=5174

# =============================================================================
# SEO AND INDEXING
# =============================================================================
# Allow search engines to index production environment
VITE_ALLOW_INDEXING=true

# =============================================================================
# CONVEX BACKEND CONFIGURATION
# =============================================================================
# Production Convex URL - provided via GitHub Actions
# VITE_CONVEX_URL=https://standing-aardvark-575.convex.cloud

# =============================================================================
# CLERK AUTHENTICATION (PRODUCTION)
# =============================================================================
# Production Clerk publishable key - provided via GitHub Actions
# VITE_CLERK_PUBLISHABLE_KEY=pk_live_...

# =============================================================================
# GOOGLE MAPS INTEGRATION
# =============================================================================
# Google Maps API key - provided via GitHub Actions
# VITE_GOOGLE_MAPS_API_KEY=AIzaSy...

# =============================================================================
# STRIPE PAYMENT PROCESSING (PRODUCTION)
# =============================================================================
# Production Stripe publishable key - provided via GitHub Actions
# VITE_STRIPE_PUBLISHABLE_KEY=pk_live_...

# Note: Actual secret values are provided via GitHub Actions environment variables
# and passed to the server during deployment. The commented lines above show
# the expected format but should not contain actual production secrets.
