# JobbLogg Production Overrides
# Use: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
# This file contains ONLY production-specific overrides

# version: '3.8'  # Removed to avoid Docker Compose v2 warnings

services:
  # Convex service placeholder in production - using cloud Convex directly
  convex:
    image: alpine:latest
    container_name: jobblogg-convex-prod-placeholder
    command: ["sh", "-c", "echo 'Production uses cloud Convex - this is a placeholder' && sleep infinity"]
    restart: "no"
    ports: []  # Remove port mapping
    volumes: []  # Remove volume mounts
    environment: []  # Remove environment variables
    healthcheck:
      test: ["CMD", "echo", "healthy"]
      interval: 30s
      timeout: 5s
      retries: 1
      start_period: 5s

  # Frontend Production Overrides
  frontend:
    build:
      context: .
      dockerfile: docker/frontend/Dockerfile
      target: production
      args:
        BUILD_ENV: ${BUILD_ENV:-production}
        VITE_CONVEX_URL: ${VITE_CONVEX_URL}
        VITE_CLERK_PUBLISHABLE_KEY: ${VITE_CLERK_PUBLISHABLE_KEY}
        VITE_GOOGLE_MAPS_API_KEY: ${VITE_GOOGLE_MAPS_API_KEY}
        VITE_STRIPE_PUBLISHABLE_KEY: ${VITE_STRIPE_PUBLISHABLE_KEY}
    # env_file removed - all environment variables are baked into build via Docker build args
    environment:
      - NODE_ENV=production
      - COMPOSE_ENV=prod
      - BUILD_TARGET=production
      # VITE_CONVEX_URL is baked into the build via Docker build args
      # No runtime environment variables needed for Vite variables
      - VITE_CLERK_PUBLISHABLE_KEY=${VITE_CLERK_PUBLISHABLE_KEY}
      - VITE_GOOGLE_MAPS_API_KEY=${VITE_GOOGLE_MAPS_API_KEY}
      - VITE_STRIPE_PUBLISHABLE_KEY=${VITE_STRIPE_PUBLISHABLE_KEY}
      - VITE_ALLOW_INDEXING=${VITE_ALLOW_INDEXING:-true}
    ports:
      - "5174:5173"  # Production frontend on 5174 (matches Caddy config)
    volumes: []  # Remove development hot-reload volumes
    depends_on: []  # Remove Convex dependency for production

# NOTE: Nginx service removed - Caddy is used as reverse proxy on the server
# Caddy configuration is managed at system level (/etc/caddy/Caddyfile)
# Production routing:
# - jobblogg.no -> localhost:5174 (frontend)
# - api.jobblogg.no -> localhost:3210 (convex)
