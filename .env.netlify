# JobbLogg Netlify Production Environment Variables
# These are set in Netlify Dashboard under Site settings > Environment variables

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
NODE_ENV=production

# =============================================================================
# CONVEX BACKEND CONFIGURATION
# =============================================================================
# Production Convex deployment
VITE_CONVEX_URL=https://standing-aardvark-575.convex.cloud

# =============================================================================
# CLERK AUTHENTICATION (PRODUCTION)
# =============================================================================
# Production Clerk publishable key
VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuam9iYmxvZ2cubm8k

# =============================================================================
# GOOGLE MAPS API
# =============================================================================
VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs

# =============================================================================
# STRIPE PAYMENT INTEGRATION (PRODUCTION)
# =============================================================================
# Production Stripe publishable key - REPLACE WITH YOUR ACTUAL KEY
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_production_stripe_key_here

# =============================================================================
# SEO AND INDEXING
# =============================================================================
VITE_ALLOW_INDEXING=true
