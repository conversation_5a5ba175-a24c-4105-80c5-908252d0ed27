# Multi-stage build for Frontend
FROM node:20-alpine as builder

# Build args (Vite + environment) - NO DEFAULT for BUILD_ENV
ARG VITE_CONVEX_URL
ARG VITE_CLERK_PUBLISHABLE_KEY
ARG VITE_GOOGLE_MAPS_API_KEY
ARG VITE_STRIPE_PUBLISHABLE_KEY
ARG BUILD_ENV  # ← NO default value here - must be explicitly provided

# Expose for Vite build process
ENV VITE_CONVEX_URL=$VITE_CONVEX_URL
ENV VITE_CLERK_PUBLISHABLE_KEY=$VITE_CLERK_PUBLISHABLE_KEY
ENV VITE_GOOGLE_MAPS_API_KEY=$VITE_GOOGLE_MAPS_API_KEY
ENV VITE_STRIPE_PUBLISHABLE_KEY=$VITE_STRIPE_PUBLISHABLE_KEY
ENV BUILD_ENV=$BUILD_ENV

# Fail fast if BUILD_ENV is not set
RUN : "${BUILD_ENV:?❌ BUILD_ENV not set (expected 'staging' or 'production')}"

# Environment-aware guard
RUN echo "Build guard (BUILD_ENV=$BUILD_ENV)..." \
 && [ -n "$VITE_CONVEX_URL" ] \
 && case "$VITE_CONVEX_URL" in https://*) : ;; *) echo "❌ Invalid VITE_CONVEX_URL"; exit 1 ;; esac \
 && if [ "$BUILD_ENV" = "production" ]; then \
      # Only forbid staging URL in PRODUCTION builds
      [ "$VITE_CONVEX_URL" != "https://enchanted-quail-174.convex.cloud" ] || { echo "❌ Staging URL in PROD build"; exit 1; }; \
    fi \
 && echo "✅ Environment variables validated"

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies for build)
RUN npm ci

# Copy source code
COPY . .

# Build the application with environment variables (skip TypeScript checking for now)
RUN npm run build -- --skipLibCheck || npm run build:no-typecheck || npm run build

# Post-build verify - environment-aware URL verification (ignore .map)
RUN echo "Post-build verify (BUILD_ENV=$BUILD_ENV)..." \
 && set -e; files="$(find dist -type f \( -name '*.js' -o -name '*.css' -o -name '*.html' \))"; \
    if [ "$BUILD_ENV" = "production" ]; then \
      echo "Checking for required PROD URL and absence of STAGING URL..."; \
      echo "$files" | xargs grep -q "https://standing-aardvark-575\.convex\.cloud" \
        || { echo "❌ Prod URL not baked into dist"; exit 1; }; \
      ! (echo "$files" | xargs grep -q "https://enchanted-quail-174\.convex\.cloud") \
        || { echo "❌ Staging URL leaked into prod dist"; exit 1; }; \
    elif [ "$BUILD_ENV" = "staging" ]; then \
      echo "Checking for required STAGING URL and absence of PROD URL..."; \
      echo "$files" | xargs grep -q "https://enchanted-quail-174\.convex\.cloud" \
        || { echo "❌ Staging URL not baked into dist"; exit 1; }; \
      ! (echo "$files" | xargs grep -q "https://standing-aardvark-575\.convex\.cloud") \
        || { echo "❌ Prod URL leaked into staging dist"; exit 1; }; \
    else \
      echo "⚠️ Unknown BUILD_ENV=$BUILD_ENV, skipping URL verification"; \
    fi \
 && echo "✅ Post-build verification passed"

# Production stage
FROM node:20-alpine as production

# Install system dependencies including curl for health checks
RUN apk add --no-cache curl

# Install serve globally for serving static files
RUN npm install -g serve

# Set working directory
WORKDIR /app

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Create a non-root user
RUN addgroup -g 1001 -S frontend && \
    adduser -S frontend -u 1001

# Change ownership
RUN chown -R frontend:frontend /app

# Switch to non-root user
USER frontend

# Expose port
EXPOSE 5173

# Health check - use curl if available, fallback to wget
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5173/ || wget --quiet --tries=1 --spider http://localhost:5173/ || exit 1

# Serve the application
CMD ["serve", "-s", "dist", "-l", "5173"]

# Development stage
FROM node:20-alpine as development

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Create a non-root user
RUN addgroup -g 1001 -S frontend && \
    adduser -S frontend -u 1001

# Change ownership
RUN chown -R frontend:frontend /app

# Switch to non-root user
USER frontend

# Expose port
EXPOSE 5173

# Start development server
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
